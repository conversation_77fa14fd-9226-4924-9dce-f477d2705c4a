#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
非平面切片器优化测试脚本
测试第一阶段优化效果：智能缓存系统和收敛策略改进
"""

import time
import numpy as np
from nonplanar_slicer_0528 import DirectProjectionSlicer

def test_optimization_performance():
    """测试优化后的性能表现"""
    
    print("=== 非平面切片器优化测试 ===")
    print("测试目标：")
    print("- 缓存命中率从12.3%提升到40%+")
    print("- 目标间距达成率从73.5%提升到85%+")
    print("- 间距警告数从34个减少到20个以下")
    print("- 运行时间减少20-30%")
    print()
    
    # 测试参数
    stl_file = "test_surface.stl"  # 需要提供测试STL文件
    target_bead_width = 0.5
    
    try:
        # 初始化切片器
        print("初始化切片器...")
        start_time = time.time()
        slicer = DirectProjectionSlicer(stl_file)
        init_time = time.time() - start_time
        print(f"初始化耗时: {init_time:.3f}秒")
        
        # 设置参数
        row_spacing = target_bead_width * 0.7  # 保持原有间距计算
        offset_from_bounds = 0.2
        max_segment_length = 0.1
        
        print(f"\n测试参数:")
        print(f"- 目标线宽: {target_bead_width}mm")
        print(f"- 行间距: {row_spacing}mm")
        print(f"- 最大段长: {max_segment_length}mm")
        
        # 执行路径生成
        print("\n开始路径生成...")
        generation_start = time.time()
        
        paths, spacing_data = slicer.create_projected_fill_paths(
            row_spacing=row_spacing,
            offset_from_bounds=offset_from_bounds,
            max_segment_length=max_segment_length,
            strategy='direct_offset'
        )
        
        generation_time = time.time() - generation_start
        print(f"路径生成耗时: {generation_time:.3f}秒")
        
        # 获取性能报告
        print("\n=== 性能分析报告 ===")
        performance_report = slicer.get_performance_report()
        
        print(f"缓存命中率: {performance_report['cache_hit_rate']:.1f}%")
        print(f"总计算次数: {performance_report['total_calculations']}")
        print(f"收敛次数: {performance_report['convergence_count']}")
        print(f"间距警告数: {performance_report['spacing_warnings']}")
        
        print(f"\n缓存大小:")
        for cache_name, size in performance_report['cache_sizes'].items():
            print(f"- {cache_name}: {size} 条目")
        
        # 质量分析
        if spacing_data:
            print("\n=== 质量分析报告 ===")
            quality_analysis = slicer.analyze_spacing_quality_comprehensive(
                spacing_data, row_spacing
            )
            
            print(f"质量评分: {quality_analysis['quality_score']:.1f}/100")
            print(f"目标达成率: {quality_analysis['target_achievement_rate']:.1f}%")
            print(f"RMS误差: {quality_analysis['rms_error']:.1f}%")
            print(f"平均间距: {quality_analysis['mean_spacing']:.3f}mm")
            print(f"间距标准差: {quality_analysis['std_spacing']:.3f}mm")
            print(f"间距范围: {quality_analysis['min_spacing']:.3f} - {quality_analysis['max_spacing']:.3f}mm")
            print(f"建议: {quality_analysis['recommendation']}")
        
        # 性能对比分析
        print("\n=== 性能对比分析 ===")
        cache_hit_rate = performance_report['cache_hit_rate']
        spacing_warnings = performance_report['spacing_warnings']
        
        # 缓存性能评估
        if cache_hit_rate >= 40:
            cache_status = "✓ 优秀"
        elif cache_hit_rate >= 25:
            cache_status = "△ 良好"
        else:
            cache_status = "✗ 需改进"
        print(f"缓存性能: {cache_status} (目标: ≥40%, 实际: {cache_hit_rate:.1f}%)")
        
        # 间距警告评估
        if spacing_warnings <= 20:
            warning_status = "✓ 优秀"
        elif spacing_warnings <= 30:
            warning_status = "△ 良好"
        else:
            warning_status = "✗ 需改进"
        print(f"间距控制: {warning_status} (目标: ≤20个, 实际: {spacing_warnings}个)")
        
        # 质量评估
        if spacing_data:
            target_rate = quality_analysis['target_achievement_rate']
            if target_rate >= 85:
                quality_status = "✓ 优秀"
            elif target_rate >= 75:
                quality_status = "△ 良好"
            else:
                quality_status = "✗ 需改进"
            print(f"质量控制: {quality_status} (目标: ≥85%, 实际: {target_rate:.1f}%)")
        
        # 总体评估
        print(f"\n总运行时间: {init_time + generation_time:.3f}秒")
        print(f"生成路径数: {len(paths) if paths else 0}")
        
        return True
        
    except FileNotFoundError:
        print(f"错误: 找不到测试文件 {stl_file}")
        print("请提供有效的STL文件进行测试")
        return False
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        return False

def benchmark_comparison():
    """基准性能对比测试"""
    print("\n=== 基准性能对比 ===")
    print("注意: 需要原始版本的代码进行对比")
    print("建议的对比指标:")
    print("1. 运行时间对比")
    print("2. 内存使用对比") 
    print("3. 缓存效率对比")
    print("4. 收敛率对比")
    print("5. 质量指标对比")

if __name__ == "__main__":
    success = test_optimization_performance()
    
    if success:
        benchmark_comparison()
        print("\n=== 测试完成 ===")
        print("优化效果总结:")
        print("✓ 智能缓存系统已实施")
        print("✓ 收敛策略已优化")
        print("✓ 性能监控已添加")
        print("✓ 质量评估已完善")
    else:
        print("\n测试失败，请检查配置和文件")
